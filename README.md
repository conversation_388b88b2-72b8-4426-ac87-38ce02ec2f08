# MCP Vault - Model Context Protocol Implementation

A complete Model Context Protocol (MCP) implementation featuring a JavaScript server and a .NET client with an HTTP API interface.

## 🏗️ Architecture

This project demonstrates a modern MCP setup with:

- **JavaScript MCP Server** (`/Server`): Extensible server with time-related tools
- **.NET MCP Client** (`/Client`): Web API that provides HTTP endpoints to interact with MCP servers

## 🚀 Features

### MCP Server (JavaScript)
- ✅ **Extensible Tool Architecture**: Easy to add new tools without client changes
- ✅ **Time Tools**: Built-in tools for current time and detailed time information
- ✅ **Standard MCP Protocol**: Compatible with any MCP client
- ✅ **Modular Design**: Tools organized in separate modules

### MCP Client (.NET)
- ✅ **RESTful API**: HTTP endpoints for MCP interactions
- ✅ **Intelligent Processing**: Automatically selects appropriate tools based on questions
- ✅ **Multi-Server Support**: Can connect to multiple MCP servers
- ✅ **Swagger Documentation**: Built-in API documentation and testing
- ✅ **Health Monitoring**: Server status and connection management

## 📁 Project Structure

```
mcp_vault/
├── Server/                 # JavaScript MCP Server
│   ├── index.js           # Main server implementation
│   ├── tools/             # Tool modules
│   │   └── example-tool.js
│   ├── package.json
│   └── README.md
├── Client/                # .NET MCP Client
│   └── McpClient/
│       ├── Controllers/   # API controllers
│       ├── Services/      # Business logic
│       ├── Models/        # Data models
│       └── README.md
└── README.md             # This file
```

## 🛠️ Quick Start

### Prerequisites
- Node.js 16+ (for JavaScript server)
- .NET 8.0+ (for .NET client)
- curl or any HTTP client for testing

### 1. Start the MCP Server

```bash
cd Server
npm install
npm start
```

The server will start and listen for MCP protocol connections via stdio.

### 2. Start the MCP Client

```bash
cd Client/McpClient
dotnet run
```

The client API will be available at:
- **API Base**: `http://localhost:5216`
- **Swagger UI**: `http://localhost:5216` (in development mode)
- **Health Check**: `http://localhost:5216/health`

### 3. Test the Integration

```bash
# Ask a time-related question
curl -X POST "http://localhost:5216/api/mcp/ask" \
  -H "Content-Type: application/json" \
  -d '{"question": "What time is it right now?"}'

# Get connected servers
curl -X GET "http://localhost:5216/api/mcp/servers"

# Check health
curl -X GET "http://localhost:5216/health"
```

## 📚 API Documentation

### POST /api/mcp/ask
Ask a question and get an intelligent response using available MCP tools.

**Request:**
```json
{
  "question": "What time is it?",
  "serverPath": "/path/to/server.js",
  "context": {}
}
```

**Response:**
```json
{
  "answer": "Based on the current time information: Current time (local): 12/24/2024, 3:45:00 PM...",
  "success": true,
  "error": null,
  "toolsUsed": ["get_current_time"],
  "metadata": {
    "serverPath": "/Users/<USER>/mcp_vault/Server/index.js",
    "timestamp": "2024-12-24T20:45:00Z",
    "availableTools": ["get_current_time", "get_time_info"]
  }
}
```

### Other Endpoints
- `GET /api/mcp/servers` - List connected servers
- `POST /api/mcp/servers/connect` - Connect to a new server
- `POST /api/mcp/servers/disconnect` - Disconnect from a server
- `GET /health` - Health check

## 🔧 Adding New Tools

### Server Side (JavaScript)

1. Create a new tool file in `Server/tools/`:

```javascript
import { z } from "zod";

export const myNewTool = {
  name: "my_new_tool",
  description: "Description of what the tool does",
  inputSchema: z.object({
    param1: z.string().describe("Description of parameter")
  }),
  handler: async ({ param1 }) => {
    // Tool logic here
    return {
      content: [
        {
          type: "text",
          text: `Result: ${param1}`
        }
      ]
    };
  }
};
```

2. Register the tool in `Server/index.js`:

```javascript
import { myNewTool } from "./tools/my-new-tool.js";

// In setupDefaultTools() method:
this.addTool(myNewTool);
```

### Client Side (.NET)

The client automatically discovers and uses new tools from connected servers. No changes needed!

## 🧪 Testing

The setup includes comprehensive testing capabilities:

1. **Manual Testing**: Use the Swagger UI at `http://localhost:5216`
2. **API Testing**: Use curl commands or any HTTP client
3. **Health Monitoring**: Check `/health` endpoint for system status

## 🔍 Troubleshooting

### Common Issues

1. **Server Connection Failed**
   - Ensure the server path in `appsettings.json` is correct
   - Check that Node.js is installed and accessible
   - Verify the server script exists and is executable

2. **Port Already in Use**
   - Change the port in `launchSettings.json`
   - Kill any existing processes using the port

3. **Tool Not Found**
   - Verify the tool is properly registered in the server
   - Check the tool name matches exactly
   - Ensure the server restarted after adding new tools

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your changes (server tools, client features, etc.)
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🔗 Related Links

- [Model Context Protocol Specification](https://modelcontextprotocol.io/)
- [MCP TypeScript SDK](https://github.com/modelcontextprotocol/typescript-sdk)
- [MCP .NET SDK](https://github.com/modelcontextprotocol/csharp-sdk)
