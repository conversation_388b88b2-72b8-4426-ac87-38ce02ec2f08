# MCP Client API

A .NET Web API that acts as a Model Context Protocol (MCP) client, providing an HTTP interface to interact with MCP servers.

## Features

- **RESTful API**: Simple HTTP endpoints for interacting with MCP servers
- **Intelligent Question Processing**: Automatically determines which tools to use based on the question
- **Multi-Server Support**: Can connect to multiple MCP servers simultaneously
- **Extensible Architecture**: Easy to add support for new MCP tools and servers
- **Swagger Documentation**: Built-in API documentation and testing interface

## API Endpoints

### POST /api/mcp/ask
Ask a question and get an intelligent response using available MCP tools.

**Request Body:**
```json
{
  "question": "What time is it?",
  "serverPath": "/path/to/server.js",
  "context": {}
}
```

**Response:**
```json
{
  "answer": "Based on the current time information: Current time (local): 12/24/2024, 3:45:00 PM...",
  "success": true,
  "error": null,
  "toolsUsed": ["get_current_time"],
  "metadata": {
    "serverPath": "/Users/<USER>/mcp_vault/Server/index.js",
    "timestamp": "2024-12-24T20:45:00Z",
    "availableTools": ["get_current_time", "get_time_info"]
  }
}
```

### GET /api/mcp/servers
Get information about connected MCP servers.

### POST /api/mcp/servers/connect
Connect to a new MCP server.

### POST /api/mcp/servers/disconnect
Disconnect from an MCP server.

### GET /api/mcp/servers/{serverPath}/tools
Get available tools from a specific server.

### GET /health
Health check endpoint.

## Running the Application

1. **Prerequisites:**
   - .NET 8.0 or higher
   - Node.js (for JavaScript MCP servers)
   - Python (for Python MCP servers)

2. **Start the application:**
   ```bash
   dotnet run
   ```

3. **Access the API:**
   - API Base URL: `https://localhost:7000` or `http://localhost:5000`
   - Swagger UI: `https://localhost:7000` (in development mode)

## Configuration

The application can be configured via `appsettings.json`:

```json
{
  "DefaultServerPath": "/path/to/your/mcp/server.js",
  "Logging": {
    "LogLevel": {
      "Default": "Information"
    }
  }
}
```

## Example Usage

### Using curl

```bash
# Ask a time-related question
curl -X POST "https://localhost:7000/api/mcp/ask" \
  -H "Content-Type: application/json" \
  -d '{"question": "What time is it right now?"}'

# Get connected servers
curl -X GET "https://localhost:7000/api/mcp/servers"

# Connect to a new server
curl -X POST "https://localhost:7000/api/mcp/servers/connect" \
  -H "Content-Type: application/json" \
  -d '"/path/to/another/server.py"'
```

### Using JavaScript/Fetch

```javascript
// Ask a question
const response = await fetch('/api/mcp/ask', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    question: 'What is the current date and time?'
  })
});

const result = await response.json();
console.log(result.answer);
```

## Architecture

The client uses a service-based architecture:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic for MCP communication
- **Models**: Data transfer objects for API contracts
- **Connection Management**: Maintains persistent connections to MCP servers

## Supported MCP Servers

The client can connect to any MCP server that follows the standard protocol:

- **JavaScript servers** (`.js` files): Executed with `node`
- **Python servers** (`.py` files): Executed with `python`
- **.NET servers** (`.csproj` projects): Executed with `dotnet run`

## Error Handling

The API provides comprehensive error handling:

- **400 Bad Request**: Invalid input parameters
- **500 Internal Server Error**: Server connection or processing errors
- **Detailed Error Messages**: Specific error information in response body

## Development

To extend the client:

1. **Add new tool support**: Modify `ProcessQuestionWithToolsAsync` in `McpClientService`
2. **Add new endpoints**: Create new controller actions
3. **Add new server types**: Extend `GetCommandAndArguments` method
4. **Customize responses**: Modify the response generation logic
