{"name": "vault-mock-api", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node --watch index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["vault", "autodesk", "mock", "api"], "author": "", "license": "ISC", "description": "Mock API server for Autodesk Vault", "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "uuid": "^11.1.0"}}