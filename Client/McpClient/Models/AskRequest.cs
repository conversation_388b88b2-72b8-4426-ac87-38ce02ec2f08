namespace McpClient.Models;

public class AskRequest
{
    public string Question { get; set; } = string.Empty;
    public string? ServerPath { get; set; }
    public Dictionary<string, object>? Context { get; set; }
}

public class AskResponse
{
    public string Answer { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? Error { get; set; }
    public List<string>? ToolsUsed { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class ServerInfo
{
    public string Name { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public List<string> AvailableTools { get; set; } = new();
    public bool IsConnected { get; set; }
    public DateTime LastConnected { get; set; }
}
