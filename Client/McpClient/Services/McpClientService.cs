using McpClient.Models;
using ModelContextProtocol;
using System.Collections.Concurrent;
using System.Text.Json;

namespace McpClient.Services;

public class McpClientService : IMcpClientService, IDisposable
{
    private readonly ILogger<McpClientService> _logger;
    private readonly ConcurrentDictionary<string, ServerConnection> _connections = new();
    private readonly string _defaultServerPath;

    public McpClientService(ILogger<McpClientService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _defaultServerPath = configuration["DefaultServerPath"] ?? "/Users/<USER>/mcp_vault/Server/index.js";
    }

    public async Task<AskResponse> AskQuestionAsync(AskRequest request)
    {
        try
        {
            var serverPath = request.ServerPath ?? _defaultServerPath;
            var connection = await GetOrCreateConnectionAsync(serverPath);

            if (connection?.McpClient == null)
            {
                return new AskResponse
                {
                    Success = false,
                    Error = "Failed to connect to MCP server",
                    Answer = "Unable to process your question due to server connection issues."
                };
            }

            var toolsUsed = new List<string>();
            var answer = await ProcessQuestionWithToolsAsync(connection.Client, request.Question, toolsUsed);

            return new AskResponse
            {
                Success = true,
                Answer = answer,
                ToolsUsed = toolsUsed,
                Metadata = new Dictionary<string, object>
                {
                    ["serverPath"] = serverPath,
                    ["timestamp"] = DateTime.UtcNow,
                    ["availableTools"] = connection.AvailableTools
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing question: {Question}", request.Question);
            return new AskResponse
            {
                Success = false,
                Error = ex.Message,
                Answer = "An error occurred while processing your question."
            };
        }
    }

    private async Task<string> ProcessQuestionWithToolsAsync(IMcpClient client, string question, List<string> toolsUsed)
    {
        // For this implementation, we'll use a simple approach to determine if we need time information
        var needsTime = ContainsTimeKeywords(question);
        
        if (needsTime)
        {
            try
            {
                var timeResult = await client.CallToolAsync("get_current_time", new Dictionary<string, object>
                {
                    ["format"] = "local"
                });
                
                toolsUsed.Add("get_current_time");
                
                if (timeResult?.Content?.Any() == true)
                {
                    var timeText = timeResult.Content.First().Text ?? "Unable to get time";
                    return $"Based on the current time information: {timeText}\n\nTo answer your question: {GenerateTimeBasedResponse(question, timeText)}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to call time tool");
            }
        }

        // If no tools are needed or tool calls failed, provide a basic response
        return GenerateBasicResponse(question);
    }

    private static bool ContainsTimeKeywords(string question)
    {
        var timeKeywords = new[] { "time", "date", "when", "now", "current", "today", "clock", "hour", "minute" };
        return timeKeywords.Any(keyword => question.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private static string GenerateTimeBasedResponse(string question, string timeInfo)
    {
        if (question.Contains("what time", StringComparison.OrdinalIgnoreCase))
            return $"The current time is: {timeInfo}";
        
        if (question.Contains("what date", StringComparison.OrdinalIgnoreCase) || 
            question.Contains("today", StringComparison.OrdinalIgnoreCase))
            return $"Today's date and time: {timeInfo}";
            
        return $"Here's the current time information you requested: {timeInfo}";
    }

    private static string GenerateBasicResponse(string question)
    {
        return $"I received your question: \"{question}\". " +
               "I'm a basic MCP client that can connect to MCP servers and use their tools. " +
               "Currently, I have access to time-related tools. " +
               "Try asking me about the current time or date!";
    }

    private async Task<ServerConnection?> GetOrCreateConnectionAsync(string serverPath)
    {
        if (_connections.TryGetValue(serverPath, out var existingConnection) &&
            existingConnection.Client != null)
        {
            return existingConnection;
        }

        try
        {
            var (command, arguments) = GetCommandAndArguments(serverPath);

            var clientTransport = new StdioClientTransport(new()
            {
                Name = "MCP Client",
                Command = command,
                Arguments = arguments,
            });

            var mcpClient = await McpClientFactory.CreateAsync(clientTransport);
            var tools = await mcpClient.ListToolsAsync();

            var connection = new ServerConnection
            {
                Client = mcpClient,
                Transport = clientTransport,
                ServerPath = serverPath,
                AvailableTools = tools.Select(t => t.Name).ToList(),
                ConnectedAt = DateTime.UtcNow
            };

            _connections.AddOrUpdate(serverPath, connection, (key, old) => connection);

            _logger.LogInformation("Connected to MCP server at {ServerPath} with tools: {Tools}",
                serverPath, string.Join(", ", connection.AvailableTools));

            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to MCP server at {ServerPath}", serverPath);
            return null;
        }
    }

    private static (string command, string[] arguments) GetCommandAndArguments(string serverPath)
    {
        return serverPath switch
        {
            var path when path.EndsWith(".py") => ("python", [path]),
            var path when path.EndsWith(".js") => ("node", [path]),
            var path when Directory.Exists(path) || (File.Exists(path) && path.EndsWith(".csproj")) =>
                ("dotnet", ["run", "--project", path, "--no-build"]),
            _ => throw new NotSupportedException($"Unsupported server script: {serverPath}")
        };
    }

    public async Task<List<ServerInfo>> GetConnectedServersAsync()
    {
        var servers = new List<ServerInfo>();

        foreach (var kvp in _connections)
        {
            servers.Add(new ServerInfo
            {
                Name = Path.GetFileNameWithoutExtension(kvp.Key),
                Path = kvp.Key,
                AvailableTools = kvp.Value.AvailableTools,
                IsConnected = kvp.Value.Client != null,
                LastConnected = kvp.Value.ConnectedAt
            });
        }

        return servers;
    }

    public async Task<bool> ConnectToServerAsync(string serverPath)
    {
        var connection = await GetOrCreateConnectionAsync(serverPath);
        return connection != null;
    }

    public async Task<bool> DisconnectFromServerAsync(string serverPath)
    {
        if (_connections.TryRemove(serverPath, out var connection))
        {
            try
            {
                connection.Client?.Dispose();
                connection.Transport?.Dispose();
                _logger.LogInformation("Disconnected from MCP server at {ServerPath}", serverPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting from server at {ServerPath}", serverPath);
            }
        }
        return false;
    }

    public async Task<List<string>> GetAvailableToolsAsync(string serverPath)
    {
        var connection = await GetOrCreateConnectionAsync(serverPath);
        return connection?.AvailableTools ?? new List<string>();
    }

    public void Dispose()
    {
        foreach (var connection in _connections.Values)
        {
            try
            {
                connection.Client?.Dispose();
                connection.Transport?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing connection");
            }
        }
        _connections.Clear();
    }

    private class ServerConnection
    {
        public McpClient? Client { get; set; }
        public IClientTransport? Transport { get; set; }
        public string ServerPath { get; set; } = string.Empty;
        public List<string> AvailableTools { get; set; } = new();
        public DateTime ConnectedAt { get; set; }
    }
}
