using McpClient.Models;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace McpClient.Services;

public class McpClientService : IMcpClientService, IDisposable
{
    private readonly ILogger<McpClientService> _logger;
    private readonly IAiService _aiService;
    private readonly ConcurrentDictionary<string, ServerConnection> _connections = new();
    private readonly string _defaultServerPath;

    public McpClientService(ILogger<McpClientService> logger, IAiService aiService, IConfiguration configuration)
    {
        _logger = logger;
        _aiService = aiService;
        _defaultServerPath = configuration["DefaultServerPath"] ?? "/Users/<USER>/mcp_vault/Server/index.js";
    }

    public async Task<AskResponse> AskQuestionAsync(AskRequest request)
    {
        try
        {
            var serverPath = request.ServerPath ?? _defaultServerPath;
            var connection = await GetOrCreateConnectionAsync(serverPath);

            if (connection?.IsConnected != true)
            {
                return new AskResponse
                {
                    Success = false,
                    Error = "Failed to connect to MCP server",
                    Answer = "Unable to process your question due to server connection issues."
                };
            }

            var toolsUsed = new List<string>();
            var answer = await ProcessQuestionWithToolsAsync(connection, request.Question, toolsUsed);

            return new AskResponse
            {
                Success = true,
                Answer = answer,
                ToolsUsed = toolsUsed,
                Metadata = new Dictionary<string, object>
                {
                    ["serverPath"] = serverPath,
                    ["timestamp"] = DateTime.UtcNow,
                    ["availableTools"] = connection.AvailableTools
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing question: {Question}", request.Question);
            return new AskResponse
            {
                Success = false,
                Error = ex.Message,
                Answer = "An error occurred while processing your question."
            };
        }
    }

    private async Task<string> ProcessQuestionWithToolsAsync(ServerConnection connection, string question, List<string> toolsUsed)
    {
        string? toolResults = null;

        // Check if we need Vault information
        var needsVault = ContainsVaultKeywords(question);
        var needsTime = ContainsTimeKeywords(question);

        if (needsVault)
        {
            try
            {
                toolResults = await CallVaultToolAsync(connection, question, toolsUsed);
                _logger.LogInformation("Successfully called Vault tool, result length: {Length}", toolResults?.Length ?? 0);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to call Vault tool");
            }
        }
        else if (needsTime && connection.AvailableTools.Contains("get_current_time"))
        {
            try
            {
                // Simulate calling the time tool by executing the server process
                toolResults = await CallServerToolAsync(connection.ServerPath, "get_current_time", new { format = "local" });
                toolsUsed.Add("get_current_time");

                _logger.LogInformation("Successfully called time tool, result: {Result}", toolResults);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to call time tool");
            }
        }

        // Use AI service to generate intelligent response
        try
        {
            var aiResponse = await _aiService.GenerateResponseAsync(question, toolResults, connection.AvailableTools);
            return aiResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate AI response, falling back to basic response");

            // Fallback to basic response if AI service fails
            if (!string.IsNullOrEmpty(toolResults))
            {
                if (needsVault)
                {
                    return $"Here's the Vault information you requested:\n\n{toolResults}";
                }
                return GenerateTimeBasedResponse(question, toolResults);
            }
            return GenerateBasicResponse(question);
        }
    }

    private static Task<string> CallServerToolAsync(string serverPath, string toolName, object parameters)
    {
        // For now, return a simulated time response
        // In a full implementation, this would communicate with the actual MCP server
        if (toolName == "get_current_time")
        {
            return Task.FromResult($"Current time (local): {DateTime.Now:F}");
        }

        return Task.FromResult("Tool result not available");
    }

    private static bool ContainsTimeKeywords(string question)
    {
        var timeKeywords = new[] {
            "time", "date", "when", "now", "current", "today", "clock", "hour", "minute",
            "which date", "what date", "what time", "which time", "day", "month", "year",
            "morning", "afternoon", "evening", "night", "timezone", "calendar"
        };
        return timeKeywords.Any(keyword => question.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private static bool ContainsVaultKeywords(string question)
    {
        var vaultKeywords = new[] {
            "vault", "file", "files", "folder", "folders", "document", "documents",
            "version", "versions", "drawing", "drawings", "assembly", "assemblies",
            "part", "parts", "cad", "dwg", "ipt", "iam", "idw", "pdf",
            "search", "find", "lookup", "browse", "list", "show me",
            "checked out", "checkout", "checkin", "latest", "recent",
            "engineering", "design", "manufacturing", "project"
        };
        return vaultKeywords.Any(keyword => question.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private async Task<string> CallVaultToolAsync(ServerConnection connection, string question, List<string> toolsUsed)
    {
        // Determine which Vault tool to use based on the question
        if (question.Contains("search", StringComparison.OrdinalIgnoreCase) &&
            connection.AvailableTools.Contains("search_vault_files"))
        {
            toolsUsed.Add("search_vault_files");
            return await SimulateVaultToolCall("search_vault_files", question);
        }

        if ((question.Contains("file", StringComparison.OrdinalIgnoreCase) ||
             question.Contains("version", StringComparison.OrdinalIgnoreCase)) &&
            connection.AvailableTools.Contains("get_vault_file_versions"))
        {
            toolsUsed.Add("get_vault_file_versions");
            return await SimulateVaultToolCall("get_vault_file_versions", question);
        }

        if (question.Contains("folder", StringComparison.OrdinalIgnoreCase) &&
            connection.AvailableTools.Contains("get_vault_folders"))
        {
            toolsUsed.Add("get_vault_folders");
            return await SimulateVaultToolCall("get_vault_folders", question);
        }

        if (question.Contains("vault", StringComparison.OrdinalIgnoreCase) &&
            connection.AvailableTools.Contains("get_vault_info"))
        {
            toolsUsed.Add("get_vault_info");
            return await SimulateVaultToolCall("get_vault_info", question);
        }

        // Default to file versions if no specific tool matches
        if (connection.AvailableTools.Contains("get_vault_file_versions"))
        {
            toolsUsed.Add("get_vault_file_versions");
            return await SimulateVaultToolCall("get_vault_file_versions", question);
        }

        return "Vault tools are available but could not determine the appropriate tool for your question.";
    }

    private async Task<List<string>> DiscoverToolsFromServerAsync(string serverPath)
    {
        try
        {
            // For this implementation, we'll use a known set of tools that the enhanced MCP server provides
            // In a full implementation, this would use the actual MCP protocol to discover tools dynamically

            var allKnownTools = new List<string>
            {
                // Time tools
                "get_current_time",
                "get_time_info",

                // Vault tools
                "get_vault_file_versions",
                "get_vault_file_version",
                "get_vault_files",
                "get_vault_folders",
                "get_vault_info",
                "search_vault_files"
            };

            _logger.LogInformation("Using known tools from enhanced MCP server: {Tools}",
                string.Join(", ", allKnownTools));

            return await Task.FromResult(allKnownTools);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get tools, using minimal default tools");
            // Fallback to minimal tools if something goes wrong
            return ["get_current_time", "get_time_info"];
        }
    }



    private static Task<string> SimulateVaultToolCall(string toolName, string question)
    {
        // Simulate calling the Vault API
        // In a real implementation, this would call the actual MCP server
        return toolName switch
        {
            "search_vault_files" => Task.FromResult("Found 5 files matching your search criteria: Assembly2.iam, Test1.ipt, Drawing1.dwg, Part1.ipt, Assembly1.iam"),
            "get_vault_file_versions" => Task.FromResult("Retrieved 10 file versions from Vault. Latest files include Assembly2.iam (v3, checked out), Test1.ipt (v2, checked in)"),
            "get_vault_folders" => Task.FromResult("Found 3 folders: ColinTest, Engineering, Manufacturing"),
            "get_vault_info" => Task.FromResult("Connected to Vault 117 (TestVault) with 52 files and 11 folders"),
            _ => Task.FromResult($"Called {toolName} successfully")
        };
    }

    private static string GenerateTimeBasedResponse(string question, string timeInfo)
    {
        // Extract just the time/date from the MCP response
        var cleanTimeInfo = ExtractTimeFromResponse(timeInfo);

        if (question.Contains("what time", StringComparison.OrdinalIgnoreCase) ||
            question.Contains("time is", StringComparison.OrdinalIgnoreCase))
            return $"The current time is {cleanTimeInfo}.";

        if (question.Contains("what date", StringComparison.OrdinalIgnoreCase) ||
            question.Contains("which date", StringComparison.OrdinalIgnoreCase) ||
            question.Contains("today", StringComparison.OrdinalIgnoreCase))
            return $"Today is {cleanTimeInfo}.";

        if (question.Contains("when", StringComparison.OrdinalIgnoreCase))
            return $"It is currently {cleanTimeInfo}.";

        return $"The current date and time is {cleanTimeInfo}.";
    }

    private static string ExtractTimeFromResponse(string timeInfo)
    {
        // Extract the actual time from "Current time (local): Tuesday, June 24, 2025 4:35:32 PM"
        if (timeInfo.Contains("Current time (local):"))
        {
            var prefix = "Current time (local):";
            var timeStart = timeInfo.IndexOf(prefix) + prefix.Length;
            var extractedTime = timeInfo[timeStart..].Trim();

            // Remove any extra formatting characters
            extractedTime = extractedTime.Replace("\u202F", " "); // Remove narrow no-break space

            return extractedTime;
        }

        return timeInfo;
    }

    private static string GenerateBasicResponse(string question)
    {
        return $"I received your question: \"{question}\". " +
               "I'm a basic MCP client that can connect to MCP servers and use their tools. " +
               "Currently, I have access to time-related tools. " +
               "Try asking me about the current time or date!";
    }

    private async Task<ServerConnection?> GetOrCreateConnectionAsync(string serverPath)
    {
        if (_connections.TryGetValue(serverPath, out var existingConnection) &&
            existingConnection.IsConnected)
        {
            return existingConnection;
        }

        try
        {
            // Discover available tools from the MCP server
            var availableTools = await DiscoverToolsFromServerAsync(serverPath);

            var connection = new ServerConnection
            {
                ServerPath = serverPath,
                AvailableTools = availableTools,
                ConnectedAt = DateTime.UtcNow,
                IsConnected = true
            };

            _connections.AddOrUpdate(serverPath, connection, (key, old) => connection);

            _logger.LogInformation("Connected to MCP server at {ServerPath} with tools: {Tools}",
                serverPath, string.Join(", ", connection.AvailableTools));

            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to MCP server at {ServerPath}", serverPath);
            return null;
        }
    }



    public Task<List<ServerInfo>> GetConnectedServersAsync()
    {
        var servers = new List<ServerInfo>();

        foreach (var kvp in _connections)
        {
            servers.Add(new ServerInfo
            {
                Name = Path.GetFileNameWithoutExtension(kvp.Key),
                Path = kvp.Key,
                AvailableTools = kvp.Value.AvailableTools,
                IsConnected = kvp.Value.IsConnected,
                LastConnected = kvp.Value.ConnectedAt
            });
        }

        return Task.FromResult(servers);
    }

    public async Task<bool> ConnectToServerAsync(string serverPath)
    {
        var connection = await GetOrCreateConnectionAsync(serverPath);
        return connection != null;
    }

    public Task<bool> DisconnectFromServerAsync(string serverPath)
    {
        if (_connections.TryRemove(serverPath, out var connection))
        {
            try
            {
                if (connection.McpClient is IDisposable disposableClient)
                    disposableClient.Dispose();
                connection.Transport?.Dispose();
                connection.IsConnected = false;
                _logger.LogInformation("Disconnected from MCP server at {ServerPath}", serverPath);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting from server at {ServerPath}", serverPath);
            }
        }
        return Task.FromResult(false);
    }

    public async Task<List<string>> GetAvailableToolsAsync(string serverPath)
    {
        var connection = await GetOrCreateConnectionAsync(serverPath);
        return connection?.AvailableTools ?? [];
    }

    public void Dispose()
    {
        foreach (var connection in _connections.Values)
        {
            try
            {
                if (connection.McpClient is IDisposable disposableClient)
                    disposableClient.Dispose();
                connection.Transport?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing connection");
            }
        }
        _connections.Clear();
        GC.SuppressFinalize(this);
    }

    private class ServerConnection
    {
        public object? McpClient { get; set; }
        public IDisposable? Transport { get; set; }
        public string ServerPath { get; set; } = string.Empty;
        public List<string> AvailableTools { get; set; } = [];
        public DateTime ConnectedAt { get; set; }
        public bool IsConnected { get; set; }
    }
}
