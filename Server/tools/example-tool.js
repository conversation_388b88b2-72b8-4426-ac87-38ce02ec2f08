import { z } from "zod";

export const exampleTool = {
  name: "example_tool",
  description: "An example tool to demonstrate how to add new tools to the server",
  inputSchema: z.object({
    message: z.string().describe("A message to process")
  }),
  handler: async ({ message }) => {
    return {
      content: [
        {
          type: "text",
          text: `Example tool processed: ${message}`
        }
      ]
    };
  }
};
