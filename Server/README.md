# MCP Server

An extensible Model Context Protocol (MCP) server built with Node.js.

## Features

- **Extensible Tool Architecture**: Easy to add new tools without modifying core server code
- **Time Tools**: Built-in tools for getting current time in various formats
- **Standard MCP Protocol**: Compatible with any MCP client

## Built-in Tools

### get_current_time
Get the current date and time in various formats.

**Parameters:**
- `format` (optional): "iso", "local", "utc", or "timestamp" (default: "iso")
- `timezone` (optional): Timezone string (e.g., "America/New_York", "Europe/London")

### get_time_info
Get detailed time information including timezone, day of week, etc.

**Parameters:**
- `timezone` (optional): Timezone string

## Running the Server

```bash
npm start
```

Or directly:
```bash
node index.js
```

## Adding New Tools

To add a new tool, create a file in the `tools/` directory following this pattern:

```javascript
import { z } from "zod";

export const myTool = {
  name: "my_tool",
  description: "Description of what the tool does",
  inputSchema: z.object({
    param1: z.string().describe("Description of parameter")
  }),
  handler: async ({ param1 }) => {
    // Tool logic here
    return {
      content: [
        {
          type: "text",
          text: `Result: ${param1}`
        }
      ]
    };
  }
};
```

Then import and register it in the server's `setupDefaultTools()` method.

## Architecture

The server uses a modular architecture where:
- Core server logic is in `index.js`
- Individual tools are in the `tools/` directory
- Tools are registered dynamically using the `addTool()` method
- Each tool defines its own schema and handler function
