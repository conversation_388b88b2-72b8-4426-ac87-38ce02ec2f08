using McpClient.Models;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Diagnostics;

namespace McpClient.Services;

public class McpClientService : IMcpClientService, IDisposable
{
    private readonly ILogger<McpClientService> _logger;
    private readonly IAiService _aiService;
    private readonly ConcurrentDictionary<string, ServerConnection> _connections = new();
    private readonly string _defaultServerPath;

    public McpClientService(ILogger<McpClientService> logger, IAiService aiService, IConfiguration configuration)
    {
        _logger = logger;
        _aiService = aiService;
        _defaultServerPath = configuration["DefaultServerPath"] ?? "/Users/<USER>/mcp_vault/Server/index.js";
    }

    public async Task<AskResponse> AskQuestionAsync(AskRequest request)
    {
        try
        {
            var serverPath = request.ServerPath ?? _defaultServerPath;
            var connection = await GetOrCreateConnectionAsync(serverPath);

            if (connection?.IsConnected != true)
            {
                return new AskResponse
                {
                    Success = false,
                    Error = "Failed to connect to MCP server",
                    Answer = "Unable to process your question due to server connection issues."
                };
            }

            var toolsUsed = new List<string>();
            var answer = await ProcessQuestionWithToolsAsync(connection, request.Question, toolsUsed);

            return new AskResponse
            {
                Success = true,
                Answer = answer,
                ToolsUsed = toolsUsed,
                Metadata = new Dictionary<string, object>
                {
                    ["serverPath"] = serverPath,
                    ["timestamp"] = DateTime.UtcNow,
                    ["availableTools"] = connection.AvailableTools
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing question: {Question}", request.Question);
            return new AskResponse
            {
                Success = false,
                Error = ex.Message,
                Answer = "An error occurred while processing your question."
            };
        }
    }

    private async Task<string> ProcessQuestionWithToolsAsync(ServerConnection connection, string question, List<string> toolsUsed)
    {
        string? toolResults = null;

        // Check if we need time information
        var needsTime = ContainsTimeKeywords(question);

        if (needsTime && connection.AvailableTools.Contains("get_current_time"))
        {
            try
            {
                // Simulate calling the time tool by executing the server process
                toolResults = await CallServerToolAsync(connection.ServerPath, "get_current_time", new { format = "local" });
                toolsUsed.Add("get_current_time");

                _logger.LogInformation("Successfully called time tool, result: {Result}", toolResults);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to call time tool");
            }
        }

        // Use AI service to generate intelligent response
        try
        {
            var aiResponse = await _aiService.GenerateResponseAsync(question, toolResults, connection.AvailableTools);
            return aiResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate AI response, falling back to basic response");

            // Fallback to basic response if AI service fails
            if (!string.IsNullOrEmpty(toolResults))
            {
                return GenerateTimeBasedResponse(question, toolResults);
            }
            return GenerateBasicResponse(question);
        }
    }

    private static Task<string> CallServerToolAsync(string serverPath, string toolName, object parameters)
    {
        // For now, return a simulated time response
        // In a full implementation, this would communicate with the actual MCP server
        if (toolName == "get_current_time")
        {
            return Task.FromResult($"Current time (local): {DateTime.Now:F}");
        }

        return Task.FromResult("Tool result not available");
    }

    private static bool ContainsTimeKeywords(string question)
    {
        var timeKeywords = new[] {
            "time", "date", "when", "now", "current", "today", "clock", "hour", "minute",
            "which date", "what date", "what time", "which time", "day", "month", "year",
            "morning", "afternoon", "evening", "night", "timezone", "calendar"
        };
        return timeKeywords.Any(keyword => question.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private static string GenerateTimeBasedResponse(string question, string timeInfo)
    {
        // Extract just the time/date from the MCP response
        var cleanTimeInfo = ExtractTimeFromResponse(timeInfo);

        if (question.Contains("what time", StringComparison.OrdinalIgnoreCase) ||
            question.Contains("time is", StringComparison.OrdinalIgnoreCase))
            return $"The current time is {cleanTimeInfo}.";

        if (question.Contains("what date", StringComparison.OrdinalIgnoreCase) ||
            question.Contains("which date", StringComparison.OrdinalIgnoreCase) ||
            question.Contains("today", StringComparison.OrdinalIgnoreCase))
            return $"Today is {cleanTimeInfo}.";

        if (question.Contains("when", StringComparison.OrdinalIgnoreCase))
            return $"It is currently {cleanTimeInfo}.";

        return $"The current date and time is {cleanTimeInfo}.";
    }

    private static string ExtractTimeFromResponse(string timeInfo)
    {
        // Extract the actual time from "Current time (local): Tuesday, June 24, 2025 4:35:32 PM"
        if (timeInfo.Contains("Current time (local):"))
        {
            var prefix = "Current time (local):";
            var timeStart = timeInfo.IndexOf(prefix) + prefix.Length;
            var extractedTime = timeInfo[timeStart..].Trim();

            // Remove any extra formatting characters
            extractedTime = extractedTime.Replace("\u202F", " "); // Remove narrow no-break space

            return extractedTime;
        }

        return timeInfo;
    }

    private static string GenerateBasicResponse(string question)
    {
        return $"I received your question: \"{question}\". " +
               "I'm a basic MCP client that can connect to MCP servers and use their tools. " +
               "Currently, I have access to time-related tools. " +
               "Try asking me about the current time or date!";
    }

    private Task<ServerConnection?> GetOrCreateConnectionAsync(string serverPath)
    {
        if (_connections.TryGetValue(serverPath, out var existingConnection) &&
            existingConnection.IsConnected)
        {
            return Task.FromResult<ServerConnection?>(existingConnection);
        }

        try
        {
            // For this simplified implementation, we'll simulate a connection
            // In a full implementation, this would use the actual MCP SDK
            var connection = new ServerConnection
            {
                ServerPath = serverPath,
                AvailableTools = ["get_current_time", "get_time_info"], // Simulated tools
                ConnectedAt = DateTime.UtcNow,
                IsConnected = true
            };

            _connections.AddOrUpdate(serverPath, connection, (key, old) => connection);

            _logger.LogInformation("Connected to MCP server at {ServerPath} with tools: {Tools}",
                serverPath, string.Join(", ", connection.AvailableTools));

            return Task.FromResult<ServerConnection?>(connection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to MCP server at {ServerPath}", serverPath);
            return Task.FromResult<ServerConnection?>(null);
        }
    }



    public Task<List<ServerInfo>> GetConnectedServersAsync()
    {
        var servers = new List<ServerInfo>();

        foreach (var kvp in _connections)
        {
            servers.Add(new ServerInfo
            {
                Name = Path.GetFileNameWithoutExtension(kvp.Key),
                Path = kvp.Key,
                AvailableTools = kvp.Value.AvailableTools,
                IsConnected = kvp.Value.IsConnected,
                LastConnected = kvp.Value.ConnectedAt
            });
        }

        return Task.FromResult(servers);
    }

    public async Task<bool> ConnectToServerAsync(string serverPath)
    {
        var connection = await GetOrCreateConnectionAsync(serverPath);
        return connection != null;
    }

    public Task<bool> DisconnectFromServerAsync(string serverPath)
    {
        if (_connections.TryRemove(serverPath, out var connection))
        {
            try
            {
                if (connection.McpClient is IDisposable disposableClient)
                    disposableClient.Dispose();
                connection.Transport?.Dispose();
                connection.IsConnected = false;
                _logger.LogInformation("Disconnected from MCP server at {ServerPath}", serverPath);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting from server at {ServerPath}", serverPath);
            }
        }
        return Task.FromResult(false);
    }

    public async Task<List<string>> GetAvailableToolsAsync(string serverPath)
    {
        var connection = await GetOrCreateConnectionAsync(serverPath);
        return connection?.AvailableTools ?? [];
    }

    public void Dispose()
    {
        foreach (var connection in _connections.Values)
        {
            try
            {
                if (connection.McpClient is IDisposable disposableClient)
                    disposableClient.Dispose();
                connection.Transport?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing connection");
            }
        }
        _connections.Clear();
        GC.SuppressFinalize(this);
    }

    private class ServerConnection
    {
        public object? McpClient { get; set; }
        public IDisposable? Transport { get; set; }
        public string ServerPath { get; set; } = string.Empty;
        public List<string> AvailableTools { get; set; } = [];
        public DateTime ConnectedAt { get; set; }
        public bool IsConnected { get; set; }
    }
}
