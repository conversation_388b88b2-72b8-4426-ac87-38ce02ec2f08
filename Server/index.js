#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

class ExtensibleMCPServer {
  constructor() {
    this.server = new McpServer({
      name: "extensible-mcp-server",
      version: "1.0.0",
      capabilities: {
        resources: {},
        tools: {},
      },
    });

    this.tools = new Map();
    this.setupDefaultTools();
    this.registerToolHandlers();
  }

  setupDefaultTools() {
    this.addTool({
      name: "get_current_time",
      description: "Get the current date and time in various formats",
      inputSchema: z.object({
        format: z.enum(["iso", "local", "utc", "timestamp"]).optional().default("iso"),
        timezone: z.string().optional().describe("Timezone (e.g., 'America/New_York', 'Europe/London')")
      }),
      handler: this.getCurrentTime.bind(this)
    });

    this.addTool({
      name: "get_time_info",
      description: "Get detailed time information including timezone, day of week, etc.",
      inputSchema: z.object({
        timezone: z.string().optional().describe("Timezone (e.g., 'America/New_York', 'Europe/London')")
      }),
      handler: this.getTimeInfo.bind(this)
    });
  }

  addTool({ name, description, inputSchema, handler }) {
    this.tools.set(name, {
      name,
      description,
      inputSchema,
      handler
    });

    this.server.tool(name, description, inputSchema.shape, handler);
  }

  async getCurrentTime({ format = "iso", timezone }) {
    try {
      const now = new Date();
      let result;

      switch (format) {
        case "iso":
          result = timezone ? 
            new Date(now.toLocaleString("en-US", { timeZone: timezone })).toISOString() :
            now.toISOString();
          break;
        case "local":
          result = timezone ?
            now.toLocaleString("en-US", { timeZone: timezone }) :
            now.toLocaleString();
          break;
        case "utc":
          result = now.toUTCString();
          break;
        case "timestamp":
          result = now.getTime().toString();
          break;
        default:
          result = now.toISOString();
      }

      return {
        content: [
          {
            type: "text",
            text: `Current time (${format}${timezone ? ` in ${timezone}` : ""}): ${result}`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error getting time: ${error.message}`
          }
        ]
      };
    }
  }

  async getTimeInfo({ timezone }) {
    try {
      const now = new Date();
      const options = timezone ? { timeZone: timezone } : {};
      
      const timeInfo = {
        iso: now.toISOString(),
        local: now.toLocaleString("en-US", options),
        utc: now.toUTCString(),
        timestamp: now.getTime(),
        dayOfWeek: now.toLocaleDateString("en-US", { ...options, weekday: "long" }),
        month: now.toLocaleDateString("en-US", { ...options, month: "long" }),
        year: now.getFullYear(),
        timezone: timezone || Intl.DateTimeFormat().resolvedOptions().timeZone
      };

      const formattedInfo = Object.entries(timeInfo)
        .map(([key, value]) => `${key}: ${value}`)
        .join("\n");

      return {
        content: [
          {
            type: "text",
            text: `Time Information:\n${formattedInfo}`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error getting time info: ${error.message}`
          }
        ]
      };
    }
  }

  registerToolHandlers() {
    console.error("Registered tools:", Array.from(this.tools.keys()));
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("MCP Server running with extensible tool architecture");
  }
}

async function main() {
  const server = new ExtensibleMCPServer();
  await server.run();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error("Server error:", error);
    process.exit(1);
  });
}

export { ExtensibleMCPServer };
