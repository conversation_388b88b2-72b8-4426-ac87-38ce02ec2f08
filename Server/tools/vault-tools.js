import { z } from "zod";
import axios from "axios";

// Vault API configuration
const VAULT_CONFIG = {
  baseUrl: process.env.VAULT_API_URL || "http://localhost:3000/AutodeskDM/Services/api/vault/v2",
  authToken: process.env.VAULT_AUTH_TOKEN || "AuIPTf4KYLTYGVnOHQ0cuolwCW2a...",
  defaultVaultId: process.env.VAULT_ID || "117"
};

// Create axios instance with default configuration
const vaultApi = axios.create({
  baseURL: VAULT_CONFIG.baseUrl,
  headers: {
    'Authorization': `Bearer ${VAULT_CONFIG.authToken}`,
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },
  timeout: 30000
});

// Helper function to format API responses
function formatVaultResponse(data, operation) {
  if (!data) {
    return `No data returned from Vault API for ${operation}`;
  }

  if (data.error) {
    return `Vault API Error: ${data.error}${data.message ? ` - ${data.message}` : ''}`;
  }

  return JSON.stringify(data, null, 2);
}

// Helper function to handle API errors
function handleVaultError(error, operation) {
  if (error.response) {
    const status = error.response.status;
    const data = error.response.data;
    return `Vault API Error (${status}): ${data?.error || data?.message || 'Unknown error'} during ${operation}`;
  } else if (error.request) {
    return `Network Error: Unable to connect to Vault API during ${operation}. Check if the Vault server is running.`;
  } else {
    return `Error during ${operation}: ${error.message}`;
  }
}

export const getVaultFileVersions = {
  name: "get_vault_file_versions",
  description: "Get file versions from Autodesk Vault with optional filtering and pagination",
  inputSchema: z.object({
    vaultId: z.string().optional().default(VAULT_CONFIG.defaultVaultId).describe("Vault ID to query"),
    limit: z.number().min(1).max(100).optional().default(10).describe("Number of results to return (1-100)"),
    filter: z.string().optional().describe("Text filter for file names, categories, or states"),
    category: z.string().optional().describe("Filter by specific category (e.g., 'Base', 'Design')"),
    state: z.string().optional().describe("Filter by file state (e.g., 'Released', 'Work in Progress')"),
    isCheckedOut: z.boolean().optional().describe("Filter by checkout status"),
    orderBy: z.string().optional().default("lastModifiedDate").describe("Field to sort by"),
    orderDirection: z.enum(["asc", "desc"]).optional().default("desc").describe("Sort direction")
  }),
  handler: async ({ vaultId, limit, filter, category, state, isCheckedOut, orderBy, orderDirection }) => {
    try {
      const params = new URLSearchParams();
      params.append('limit', limit.toString());
      
      if (filter) params.append('filter', filter);
      if (category) params.append('category', category);
      if (state) params.append('state', state);
      if (isCheckedOut !== undefined) params.append('isCheckedOut', isCheckedOut.toString());
      if (orderBy !== 'lastModifiedDate') params.append('orderBy', orderBy);
      if (orderDirection !== 'desc') params.append('orderDirection', orderDirection);

      const response = await vaultApi.get(`/vaults/${vaultId}/file-versions?${params.toString()}`);
      
      const data = response.data;
      const resultCount = data.results?.length || 0;
      const totalResults = data.pagination?.totalResults || 0;
      
      let summary = `Found ${resultCount} file versions (${totalResults} total)`;
      if (filter || category || state || isCheckedOut !== undefined) {
        const filters = [];
        if (filter) filters.push(`text: "${filter}"`);
        if (category) filters.push(`category: "${category}"`);
        if (state) filters.push(`state: "${state}"`);
        if (isCheckedOut !== undefined) filters.push(`checked out: ${isCheckedOut}`);
        summary += ` with filters: ${filters.join(', ')}`;
      }

      return {
        content: [
          {
            type: "text",
            text: `${summary}\n\n${formatVaultResponse(data, 'get file versions')}`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: handleVaultError(error, 'get file versions')
          }
        ]
      };
    }
  }
};

export const getVaultFileVersion = {
  name: "get_vault_file_version",
  description: "Get a specific file version from Autodesk Vault by ID",
  inputSchema: z.object({
    vaultId: z.string().optional().default(VAULT_CONFIG.defaultVaultId).describe("Vault ID"),
    fileVersionId: z.string().describe("File version ID to retrieve")
  }),
  handler: async ({ vaultId, fileVersionId }) => {
    try {
      const response = await vaultApi.get(`/vaults/${vaultId}/file-versions/${fileVersionId}`);
      
      const data = response.data;
      const fileName = data.name || 'Unknown';
      const version = data.version || 'Unknown';
      const state = data.state || 'No state';
      
      return {
        content: [
          {
            type: "text",
            text: `File Version Details for "${fileName}" (v${version}, state: ${state})\n\n${formatVaultResponse(data, 'get file version')}`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: handleVaultError(error, 'get file version')
          }
        ]
      };
    }
  }
};

export const getVaultFiles = {
  name: "get_vault_files",
  description: "Get files from Autodesk Vault with optional filtering",
  inputSchema: z.object({
    vaultId: z.string().optional().default(VAULT_CONFIG.defaultVaultId).describe("Vault ID to query"),
    limit: z.number().min(1).max(100).optional().default(10).describe("Number of results to return"),
    filter: z.string().optional().describe("Text filter for file names")
  }),
  handler: async ({ vaultId, limit, filter }) => {
    try {
      const params = new URLSearchParams();
      params.append('limit', limit.toString());
      if (filter) params.append('filter', filter);

      const response = await vaultApi.get(`/vaults/${vaultId}/files?${params.toString()}`);
      
      const data = response.data;
      const resultCount = data.results?.length || 0;
      
      let summary = `Found ${resultCount} files`;
      if (filter) {
        summary += ` matching "${filter}"`;
      }

      return {
        content: [
          {
            type: "text",
            text: `${summary}\n\n${formatVaultResponse(data, 'get files')}`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: handleVaultError(error, 'get files')
          }
        ]
      };
    }
  }
};

export const getVaultFolders = {
  name: "get_vault_folders",
  description: "Get folders from Autodesk Vault with optional filtering",
  inputSchema: z.object({
    vaultId: z.string().optional().default(VAULT_CONFIG.defaultVaultId).describe("Vault ID to query"),
    limit: z.number().min(1).max(100).optional().default(10).describe("Number of results to return"),
    filter: z.string().optional().describe("Text filter for folder names"),
    parentId: z.string().optional().describe("Parent folder ID to filter by")
  }),
  handler: async ({ vaultId, limit, filter, parentId }) => {
    try {
      const params = new URLSearchParams();
      params.append('limit', limit.toString());
      if (filter) params.append('filter', filter);
      if (parentId) params.append('parentId', parentId);

      const response = await vaultApi.get(`/vaults/${vaultId}/folders?${params.toString()}`);
      
      const data = response.data;
      const resultCount = data.results?.length || 0;
      
      let summary = `Found ${resultCount} folders`;
      if (filter) summary += ` matching "${filter}"`;
      if (parentId) summary += ` in parent folder ${parentId}`;

      return {
        content: [
          {
            type: "text",
            text: `${summary}\n\n${formatVaultResponse(data, 'get folders')}`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: handleVaultError(error, 'get folders')
          }
        ]
      };
    }
  }
};

export const getVaultInfo = {
  name: "get_vault_info",
  description: "Get information about available Autodesk Vaults",
  inputSchema: z.object({
    vaultId: z.string().optional().describe("Specific vault ID to get info for, or leave empty for all vaults")
  }),
  handler: async ({ vaultId }) => {
    try {
      const endpoint = vaultId ? `/vaults/${vaultId}` : '/vaults';
      const response = await vaultApi.get(endpoint);
      
      const data = response.data;
      
      if (vaultId) {
        return {
          content: [
            {
              type: "text",
              text: `Vault Information for ID ${vaultId}\n\n${formatVaultResponse(data, 'get vault info')}`
            }
          ]
        };
      } else {
        const vaultCount = data.results?.length || 0;
        return {
          content: [
            {
              type: "text",
              text: `Found ${vaultCount} available vaults\n\n${formatVaultResponse(data, 'get vaults')}`
            }
          ]
        };
      }
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: handleVaultError(error, 'get vault info')
          }
        ]
      };
    }
  }
};

export const searchVaultFiles = {
  name: "search_vault_files",
  description: "Search for files in Autodesk Vault with advanced filtering options",
  inputSchema: z.object({
    query: z.string().describe("Search query for file names, categories, or states"),
    vaultId: z.string().optional().default(VAULT_CONFIG.defaultVaultId).describe("Vault ID to search in"),
    limit: z.number().min(1).max(50).optional().default(20).describe("Maximum number of results"),
    fileType: z.string().optional().describe("Filter by file extension (e.g., 'ipt', 'iam', 'dwg')"),
    checkedOutOnly: z.boolean().optional().describe("Only show checked out files"),
    recentOnly: z.boolean().optional().describe("Only show recently modified files")
  }),
  handler: async ({ query, vaultId, limit, fileType, checkedOutOnly, recentOnly }) => {
    try {
      const params = new URLSearchParams();
      params.append('limit', limit.toString());
      params.append('filter', query);
      
      if (checkedOutOnly) params.append('isCheckedOut', 'true');
      if (recentOnly) {
        params.append('orderBy', 'lastModifiedDate');
        params.append('orderDirection', 'desc');
      }

      const response = await vaultApi.get(`/vaults/${vaultId}/file-versions?${params.toString()}`);
      
      let results = response.data.results || [];
      
      // Client-side filtering for file type if specified
      if (fileType) {
        const extension = fileType.startsWith('.') ? fileType : `.${fileType}`;
        results = results.filter(file => 
          file.name && file.name.toLowerCase().endsWith(extension.toLowerCase())
        );
      }

      const resultCount = results.length;
      const totalFound = response.data.pagination?.totalResults || 0;
      
      let summary = `Search Results: Found ${resultCount} files`;
      if (fileType) summary += ` (${fileType} files only)`;
      if (checkedOutOnly) summary += ` (checked out only)`;
      if (recentOnly) summary += ` (recent files first)`;
      summary += ` matching "${query}"`;

      const responseData = {
        ...response.data,
        results: results
      };

      return {
        content: [
          {
            type: "text",
            text: `${summary}\n\n${formatVaultResponse(responseData, 'search files')}`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: handleVaultError(error, 'search files')
          }
        ]
      };
    }
  }
};
