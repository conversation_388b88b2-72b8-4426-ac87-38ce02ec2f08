{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#", "description": "Meta-schema for $data reference (JSON Schema extension proposal)", "type": "object", "required": ["$data"], "properties": {"$data": {"type": "string", "anyOf": [{"format": "relative-json-pointer"}, {"format": "json-pointer"}]}}, "additionalProperties": false}