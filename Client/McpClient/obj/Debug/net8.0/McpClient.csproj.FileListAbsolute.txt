/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.csproj.AssemblyReference.cache
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.AssemblyInfoInputs.cache
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.AssemblyInfo.cs
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.csproj.CoreCompileInputs.cache
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.MvcApplicationPartsAssemblyInfo.cs
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.MvcApplicationPartsAssemblyInfo.cache
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/appsettings.Development.json
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/appsettings.json
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/McpClient
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/McpClient.deps.json
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/McpClient.runtimeconfig.json
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/McpClient.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/McpClient.pdb
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.AspNetCore.OpenApi.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.AI.Abstractions.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Configuration.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Binder.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Json.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Diagnostics.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.FileProviders.Physical.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Hosting.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Logging.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Logging.Abstractions.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Logging.Configuration.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Logging.Console.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Logging.Debug.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Logging.EventLog.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Logging.EventSource.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Options.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.Extensions.Primitives.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Microsoft.OpenApi.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/ModelContextProtocol.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/ModelContextProtocol.Core.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Swashbuckle.AspNetCore.Swagger.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/System.Diagnostics.DiagnosticSource.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/System.Diagnostics.EventLog.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/System.IO.Pipelines.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/System.Net.ServerSentEvents.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/System.Text.Json.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll
/Users/<USER>/mcp_vault/Client/McpClient/bin/Debug/net8.0/runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/staticwebassets.build.json
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/staticwebassets.development.json
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/staticwebassets/msbuild.McpClient.Microsoft.AspNetCore.StaticWebAssets.props
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/staticwebassets/msbuild.build.McpClient.props
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.McpClient.props
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.McpClient.props
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/staticwebassets.pack.json
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/scopedcss/bundle/McpClient.styles.css
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.csproj.Up2Date
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.dll
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/refint/McpClient.dll
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.pdb
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/McpClient.genruntimeconfig.cache
/Users/<USER>/mcp_vault/Client/McpClient/obj/Debug/net8.0/ref/McpClient.dll
